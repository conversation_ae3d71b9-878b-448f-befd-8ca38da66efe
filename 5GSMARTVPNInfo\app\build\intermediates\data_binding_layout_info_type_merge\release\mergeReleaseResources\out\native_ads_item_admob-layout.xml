<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="native_ads_item_admob" modulePackage="com.official.fivegfastvpn" filePath="app\src\main\res\layout\native_ads_item_admob.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.gms.ads.nativead.NativeAdView" rootNodeViewId="@+id/llparent"><Targets><Target id="@+id/llparent" tag="layout/native_ads_item_admob_0" originalTag="layout/row_video_list_native_ad_0" view="com.google.android.gms.ads.nativead.NativeAdView"><Expressions/><location startLine="1" startOffset="0" endLine="224" endOffset="50"/></Target><Target id="@+id/cv_video_thumb" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="10" startOffset="4" endLine="222" endOffset="39"/></Target><Target id="@+id/rladView" view="RelativeLayout"><Expressions/><location startLine="18" startOffset="8" endLine="220" endOffset="24"/></Target><Target id="@+id/rladtext" view="RelativeLayout"><Expressions/><location startLine="27" startOffset="12" endLine="50" endOffset="28"/></Target><Target id="@+id/removeadd" view="ImageView"><Expressions/><location startLine="52" startOffset="12" endLine="60" endOffset="40"/></Target><Target id="@+id/ad_media" view="com.google.android.gms.ads.nativead.MediaView"><Expressions/><location startLine="73" startOffset="16" endLine="78" endOffset="58"/></Target><Target id="@+id/ad_body" view="TextView"><Expressions/><location startLine="80" startOffset="16" endLine="91" endOffset="47"/></Target><Target id="@+id/llbottom" view="LinearLayout"><Expressions/><location startLine="97" startOffset="12" endLine="218" endOffset="26"/></Target><Target id="@+id/llprice" view="LinearLayout"><Expressions/><location startLine="104" startOffset="16" endLine="132" endOffset="30"/></Target><Target id="@+id/ad_price" view="TextView"><Expressions/><location startLine="111" startOffset="20" endLine="120" endOffset="51"/></Target><Target id="@+id/ad_store" view="TextView"><Expressions/><location startLine="122" startOffset="20" endLine="131" endOffset="51"/></Target><Target id="@+id/profile" view="RelativeLayout"><Expressions/><location startLine="134" startOffset="16" endLine="194" endOffset="32"/></Target><Target id="@+id/ad_icon" view="ImageView"><Expressions/><location startLine="141" startOffset="20" endLine="147" endOffset="51"/></Target><Target id="@+id/ad_headline" view="TextView"><Expressions/><location startLine="158" startOffset="24" endLine="168" endOffset="54"/></Target><Target id="@+id/ad_advertiser" view="TextView"><Expressions/><location startLine="170" startOffset="24" endLine="180" endOffset="55"/></Target><Target id="@+id/ad_stars" view="RatingBar"><Expressions/><location startLine="182" startOffset="24" endLine="192" endOffset="62"/></Target><Target id="@+id/rl_install" view="RelativeLayout"><Expressions/><location startLine="196" startOffset="16" endLine="217" endOffset="32"/></Target><Target id="@+id/ad_call_to_action" view="Button"><Expressions/><location startLine="202" startOffset="20" endLine="216" endOffset="50"/></Target></Targets></Layout>