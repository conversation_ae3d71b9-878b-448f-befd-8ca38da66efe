<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppThemeDark" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/black</item>
    </style>
    <style name="Base.Theme._5GSMARTVPNInfo" parent="Theme.Material3.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="CustomSearchViewStyle" parent="Widget.AppCompat.SearchView">
        
        <item name="android:textColorHint">@color/black</item>
    </style>
    <style name="Normal">
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">12sp</item>
    </style>
    <style name="RatingBar" parent="@style/Theme.AppCompat">
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlNormal">@color/gray</item>
    </style>
    <style name="Theme._5GMasterVpn" parent="Base.Theme._5GSMARTVPNInfo"/>
    <style name="Title">
        <item name="android:textColor">@color/black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="dialog" parent="android:Theme.DeviceDefault.Light.Dialog"/>
</resources>