<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".pro.PremiumActivity">

    <ImageButton
        android:id="@+id/btn_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="8dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_close"
        android:contentDescription="Close" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/btn_close"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Try Premium Free for 3 Days—\nEnjoy the Upgrade"
                android:textAlignment="center"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="32dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="32dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_servers"
                        android:layout_marginEnd="16dp"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Premium Servers"
                        android:textSize="16sp"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_speed"
                        android:layout_marginEnd="16dp"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Fast Connection"
                        android:textSize="16sp"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_unlimited"
                        android:layout_marginEnd="16dp"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Unlimited Connectivity"
                        android:textSize="16sp"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_no_ads"
                        android:layout_marginEnd="16dp"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No Ads Ever"
                        android:textSize="16sp"/>
                </LinearLayout>
            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/annual_plan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Annual Plan"
                            android:textStyle="bold"
                            android:textSize="16sp"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Start 3 days free"
                            android:textColor="@android:color/darker_gray"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="40 % Discount"
                            android:textColor="@color/colorAccent"
                            android:background="@drawable/discount_badge"/>
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="R$119.99"
                        android:textStyle="bold"
                        android:textSize="18sp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/monthly_plan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Monthly Plan"
                            android:textStyle="bold"
                            android:textSize="16sp"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Start 3 days free"
                            android:textColor="@android:color/darker_gray"/>
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="R$16.99"
                        android:textStyle="bold"
                        android:textSize="18sp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_subscribe"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Subscribe now"
                android:textAllCaps="false"
                android:padding="16dp"
                android:layout_marginBottom="16dp"
                app:cornerRadius="8dp"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="By continuing, you agree to our Privacy Policy."
                android:textAlignment="center"
                android:textSize="12sp"
                android:layout_marginBottom="8dp"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="After the 3-day trial, R$119.99/year will be billed through Google account. You can cancel anytime. Please cancel at least 24 hours before the trial ends to avoid charges. If you're unsure how to cancel or manage your subscription, please visit our help center or Google Play's Subscription Center. Deleting the app does not cancel your subscription. Consider setting a reminder."
                android:textAlignment="center"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"/>
        </LinearLayout>
    </ScrollView>
</RelativeLayout>