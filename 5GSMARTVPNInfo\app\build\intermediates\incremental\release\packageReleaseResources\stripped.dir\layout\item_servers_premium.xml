<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="7dp"
    android:layout_marginTop="5dp"
    android:layout_marginEnd="7dp"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="7dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp">

        <ImageView
            android:id="@+id/flag"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="5dp"
            android:background="@drawable/white_bg"
            android:padding="2dp"
            tools:src="@drawable/ic_japan" />

        <TextView
            android:id="@+id/countryName"
            style="@style/Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:layout_toStartOf="@id/primeLg"
            android:layout_toEndOf="@+id/flag"
            android:gravity="start"
            android:textColor="@color/text"
            android:textSize="16sp"
            tools:text="Japan - Tokyo" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/primeLg"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="10dp"
            android:background="@drawable/upgrade" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>

