package com.official.fivegfastvpn.fragments;

import static android.app.Activity.RESULT_OK;

import android.app.AlertDialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.VpnService;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.RemoteException;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.airbnb.lottie.LottieAnimationView;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.bumptech.glide.Glide;
import com.facebook.ads.NativeAdLayout;
import com.official.fivegfastvpn.BuildConfig;
import com.official.fivegfastvpn.MainActivity;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.activity.NotificationsActivity;
import com.official.fivegfastvpn.activity.ServersActivity;
import com.official.fivegfastvpn.ads.AdCall;
import com.official.fivegfastvpn.ads.AdCode;
import com.official.fivegfastvpn.ads.Admob;
import com.official.fivegfastvpn.ads.AdsHelper;
import com.official.fivegfastvpn.api.Const;
import com.official.fivegfastvpn.interfaces.ChangeServer;
import com.official.fivegfastvpn.model.Server;
import com.official.fivegfastvpn.pro.PremiumActivity;
import com.official.fivegfastvpn.pro.ProConfig;
import com.official.fivegfastvpn.service.VpnTimerService;
import com.official.fivegfastvpn.activity.VpnWarningActivity;

import com.official.fivegfastvpn.utils.CheckInternetConnection;
import com.official.fivegfastvpn.utils.Pref;
import com.official.fivegfastvpn.utils.Utils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import de.blinkt.openvpn.OpenVpnApi;
import de.blinkt.openvpn.OpenVpnApiV2;
import de.blinkt.openvpn.core.OpenVPNService;
import de.blinkt.openvpn.core.OpenVPNServiceV2;
import de.blinkt.openvpn.core.OpenVPNThread;
import de.blinkt.openvpn.core.VpnStatus;

import android.content.SharedPreferences;
import android.preference.PreferenceManager;

//Developer :--Md Sadrul Hasan Dider
public class MainFragment extends Fragment implements ChangeServer {

    private static final String TAG = "MainFragment";

    private Server server;
    private CheckInternetConnection connection;
    private OpenVPNThread vpnThread = new OpenVPNThread();
    private OpenVPNService vpnService = new OpenVPNService();
    private boolean vpnStart = false;
    private View mView;
    private Pref pref;
    private static final int REQUEST_CODE = 101;
    private Handler timerHandler = new Handler();
    private int remainingTimeInSeconds = 0;
    private Runnable timerRunnable;
    private long timerStartTime = 0;
    private boolean isRestoringState = false;
    private Button renewButton;

    // CRITICAL FIX: Ad throttling mechanism to prevent infinite loops
    private boolean hasShownAdsThisSession = false;
    private long lastAdShownTime = 0;
    private static final long AD_COOLDOWN_PERIOD = 30000; // 30 seconds between ads
    private boolean hasInitialStateDetection = false;
    private Button extraTime;

    // UI Elements
    private ImageView category;
    private ImageView premium;
    private ImageView notification;
    private LinearLayout pre;
    private LinearLayout purchaseLayout;
    private RelativeLayout currentConnectionLayout;
    private ImageView selectedServerIcon;
    private TextView countryName;
    private TextView ipTv;
    private TextView protectionStatus;
    private TextView durationTv;
    private LottieAnimationView btnConnect;
    private LinearLayout renewButtonLayout;
    private TextView logTv;
    private LinearLayout statsLayout; // Add this to class-level declarations
    private TextView byteInTv;
    private TextView byteOutTv;
    private Handler speedUpdateHandler = new Handler();
    private Runnable speedUpdateRunnable;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (mView == null) {
            mView = inflater.inflate(R.layout.fragment_main, container, false);

            initializeViews();
            initializeAll();

            if (ProConfig.isPremium(mView.getContext())) {
                purchaseLayout.setVisibility(View.VISIBLE);
                purchaseLayout.setOnClickListener(view -> {
                    Intent intent = new Intent(getActivity(), ServersActivity.class);
                    startActivity(intent);
                });

                // Show currentConnectionLayout only for premium users
                currentConnectionLayout.setVisibility(View.VISIBLE);

                // Hide premium ImageView for premium users
                premium.setVisibility(View.GONE);
            } else {
                purchaseLayout.setVisibility(View.GONE);

                // Hide currentConnectionLayout for non-premium users
                currentConnectionLayout.setVisibility(View.GONE);

                // Ensure premium ImageView is visible for non-premium users
                premium.setVisibility(View.VISIBLE);
            }

            category.setOnClickListener(view -> {
                if (getActivity() != null) ((MainActivity) getActivity()).openCloseDrawer();
            });
            premium.setOnClickListener(view -> {
                Intent intent = new Intent(getActivity(), PremiumActivity.class);
                startActivity(intent);
            });

            notification.setOnClickListener(view -> {
                Intent intent = new Intent(getActivity(), NotificationsActivity.class);
                startActivity(intent);
            });

        } else {
            if (mView.getParent() != null) {
                ((ViewGroup) mView.getParent()).removeView(mView);
            }
        }

        loadNative();

        return mView;
    }

    @Override
    public void onResume() {
        super.onResume();

        // CRITICAL FIX: Only re-check VPN state if we haven't already done initial detection
        // This prevents excessive state checking and ad triggering
        if (mView != null && !isRestoringState && !hasInitialStateDetection) {
            Log.d(TAG, "Fragment resumed - performing VPN state check");
            new android.os.Handler().postDelayed(() -> {
                detectAndRestoreVpnState();
            }, 500); // Small delay to ensure UI is ready
        } else {
            Log.d(TAG, "Fragment resumed - skipping state check (already done or in progress)");
        }
    }

    private void initializeViews() {
        category = mView.findViewById(R.id.category);
        premium = mView.findViewById(R.id.premium);
        notification = mView.findViewById(R.id.notification);
        pre = mView.findViewById(R.id.pre);
        purchaseLayout = mView.findViewById(R.id.purchase_layout);
        currentConnectionLayout = mView.findViewById(R.id.currentConnectionLayout);
        selectedServerIcon = mView.findViewById(R.id.selectedServerIcon);
        countryName = mView.findViewById(R.id.countryName);
        ipTv = mView.findViewById(R.id.ipAddress);
        protectionStatus = mView.findViewById(R.id.protectionStatus);
        durationTv = mView.findViewById(R.id.durationTv);
        btnConnect = mView.findViewById(R.id.btnConnect);
        renewButtonLayout = mView.findViewById(R.id.renewButtonLayout);
        renewButton = mView.findViewById(R.id.btnRenew);
        extraTime = mView.findViewById(R.id.extraTime);
        logTv = mView.findViewById(R.id.logTv);
        statsLayout = mView.findViewById(R.id.statsLayout); // Add this to initializeViews method
        byteInTv = mView.findViewById(R.id.byteInTv);
        byteOutTv = mView.findViewById(R.id.byteOutTv);

        // Make sure renew button is visible
        if (renewButtonLayout != null) {
            renewButtonLayout.setVisibility(View.VISIBLE);
            Log.d(TAG, "Renew button initialized and set visible");
        } else {
            Log.e(TAG, "Renew button is null after initialization");
        }
    }

    private void loadNative() {
        RelativeLayout native_main_container = mView.findViewById(R.id.native_main_container);
        FrameLayout adsLayout = mView.findViewById(R.id.native_frame);
        RelativeLayout noAdsLayout = mView.findViewById(R.id.rlNoadView);
        NativeAdLayout facebookNativeLayout = mView.findViewById(R.id.native_ad_container);

        // Check if the user is a premium user before loading ads
        if (!ProConfig.isPremium(getActivity())) {
            AdCode.loadNativeAd(native_main_container, adsLayout, facebookNativeLayout, noAdsLayout, getActivity());
        } else {
            // If premium, hide the ads layout and show the no-ads layout
            native_main_container.setVisibility(View.GONE);
            adsLayout.setVisibility(View.GONE);
            noAdsLayout.setVisibility(View.VISIBLE);
        }
    }

    private void initializeAll() {
        // Initialize Pref
        pref = new Pref(getContext());

        // CRITICAL FIX: Detect and restore VPN state on startup
        detectAndRestoreVpnState();

        ((MainActivity) getActivity()).defaultServer.observe(getActivity(), currentServer -> {
            // CRITICAL FIX: Check if server actually changed to prevent timer reset
            boolean serverChanged = (server == null || !server.equals(currentServer));
            Log.d(TAG, "TIMER DEBUG: Server observer triggered - serverChanged = " + serverChanged);
            Log.d(TAG, "TIMER DEBUG: Server observer - vpnStart = " + vpnStart);

            server = currentServer; // Update server reference

            if (vpnStart && serverChanged) {
                Log.d(TAG, "TIMER DEBUG: Server changed while VPN connected, updating status");
                setStatus("CONNECTED");
            } else if (!vpnStart) {
                setStatus("DISCONNECTED");
            } else {
                Log.d(TAG, "TIMER DEBUG: Server observer triggered but no server change, skipping status update");
            }

            countryName.setText(currentServer.getCountry());
            updateCurrentVipServerIcon(currentServer.getFlagUrl());

            if (((MainActivity) getActivity()).isActivateServer()) {
                prepareVpn();
            }
        });

        connection = new CheckInternetConnection();
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(broadcastReceiver, new IntentFilter("connectionState"));
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(trafficStatsReceiver, new IntentFilter("trafficStats"));

        // Register receivers for timer service communication
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(timerUpdateReceiver, new IntentFilter("vpn_timer_update"));
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(timerExpiredReceiver, new IntentFilter("vpn_timer_expired"));
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(timeExtendedReceiver, new IntentFilter("vpn_time_extended"));
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(timerExtendedReceiver, new IntentFilter("vpn_timer_extended"));

        // CRITICAL FIX: Register receiver for native OpenVPN fallback
        IntentFilter nativeOpenVpnFilter = new IntentFilter("START_NATIVE_OPENVPN");

        // Fix for Android 14+ (API 34+): Add RECEIVER_NOT_EXPORTED flag
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            getActivity().registerReceiver(nativeOpenVpnReceiver, nativeOpenVpnFilter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            getActivity().registerReceiver(nativeOpenVpnReceiver, nativeOpenVpnFilter);
        }

        // CRITICAL FIX: Delay IP fetching to ensure fragment is fully attached
        new Handler().postDelayed(() -> {
            if (isAdded() && !isDetached()) {
                getIP();
            }
        }, 500);

        // Load initial rewarded ad
        Log.d(TAG, "Loading initial rewarded ad");
        Admob.loadRew(getActivity());

        // Set up renew button click listener
//        if (renewButtonLayout != null) {
        renewButton.setOnClickListener(v -> {
            Log.d(TAG, "Renew button clicked");
            showToast("Loading reward video...");

            if (AdCode.rewardedAd != null) {
                Log.d(TAG, "Showing rewarded ad");
                Admob.showRewarded(getActivity(), new AdCall() {
                    @Override
                    public void next() {
                        Log.d(TAG, "Reward ad completed successfully");
                        // Handle successful reward
                        if (AdsHelper.reward_time != null && !AdsHelper.reward_time.isEmpty()) {
                            try {
                                int minutes = Integer.parseInt(AdsHelper.reward_time);
                                int additionalSeconds = minutes * 60;

                                Log.d(TAG, "TIMER DEBUG: Adding " + minutes + " minutes (" + additionalSeconds + " seconds) to existing timer");
                                Log.d(TAG, "TIMER DEBUG: Current remainingTimeInSeconds = " + remainingTimeInSeconds);

                                // CRITICAL FIX: Extend timer instead of restarting it
                                // Update local variable for immediate UI update
                                remainingTimeInSeconds += additionalSeconds;

                                // Send extension request to VPN timer service
                                Intent timerExtensionIntent = new Intent(getContext(), VpnTimerService.class);
                                timerExtensionIntent.putExtra("extend_timer", true);
                                timerExtensionIntent.putExtra("additional_seconds", additionalSeconds);

                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                    getContext().startForegroundService(timerExtensionIntent);
                                } else {
                                    getContext().startService(timerExtensionIntent);
                                }

                                updateTimerDisplay(); // Update display immediately after adding time
                                showToast("Added " + minutes + " minutes to your time!");
                                Log.d(TAG, "TIMER DEBUG: Extended timer by " + minutes + " minutes. New total: " + (remainingTimeInSeconds / 60) + " minutes");
                                Log.d(TAG, "TIMER DEBUG: New remainingTimeInSeconds = " + remainingTimeInSeconds);
                            } catch (NumberFormatException e) {
                                e.printStackTrace();
                                showToast("Error renewing time");
                                Log.e(TAG, "Error parsing reward time: " + AdsHelper.reward_time);
                            }
                        }
                    }

                    @Override
                    public void failed() {
                        Log.e(TAG, "Failed to show reward ad");
                        showToast("Failed to load reward ad. Please try again later.");
                        Admob.loadRew(getActivity()); // Try to load a new ad for next time
                    }
                });
            } else {
                Log.d(TAG, "Rewarded ad not loaded yet");
                showToast("Ad not ready yet. Please try again in a moment.");
                Admob.loadRew(getActivity());
            }
        });
        Log.d(TAG, "Renew button click listener set up");
//        } else {
//            Log.e(TAG, "Renew button is null in initializeAll");
//        }
    }

    private void showInter() {
        // CRITICAL FIX: Add ad throttling to prevent infinite loops
        if (!canShowAds()) {
            Log.d(TAG, "Ad throttling active - skipping interstitial ad");
            return;
        }

        // Only show interstitial ads for non-premium users
        if (!ProConfig.isPremium(getActivity())) {
            Log.d(TAG, "Showing interstitial ad with throttling");
            markAdShown();

            AdCode.showInt(getActivity(), new AdCall() {
                @Override
                public void next() {
                    Log.d(TAG, "Interstitial ad completed - NOT showing reward ad to prevent loop");
                    // CRITICAL FIX: Don't show reward ad after interstitial to prevent infinite loop
                    // showRewardAdAfterInterstitial(); // REMOVED
                }

                @Override
                public void failed() {
                    Log.d(TAG, "Interstitial ad failed - NOT showing reward ad to prevent loop");
                    // CRITICAL FIX: Don't show reward ad if interstitial fails to prevent infinite loop
                    // showRewardAdAfterInterstitial(); // REMOVED
                }
            });
        } else {
            // For premium users, no ads but still load reward ads for renew functionality
            AdCode.loadRew(getActivity());
        }
    }

    /**
     * Show reward ad after interstitial ad sequence
     */
    private void showRewardAdAfterInterstitial() {
        if (!AdsHelper.isDisableAds(getContext())) {
            Log.d(TAG, "Loading reward ad after interstitial sequence");

            // Load and show reward ad after a slight delay
            new Handler().postDelayed(() -> {
                AdCode.loadRew(getActivity());

                new Handler().postDelayed(() -> {
                    AdCode.showRew(getActivity(), new AdCall() {
                        @Override
                        public void next() {
                            Log.d(TAG, "Rewarded ad shown successfully after interstitial");
                        }

                        @Override
                        public void failed() {
                            Log.d(TAG, "Rewarded ad failed to show after interstitial");
                        }
                    }, false);
                }, 1500); // Shorter delay since interstitial already shown
            }, 500);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Handle back button press
        requireActivity().getOnBackPressedDispatcher().addCallback(getViewLifecycleOwner(), new androidx.activity.OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // Minimize app to background without disconnecting VPN
                Intent startMain = new Intent(Intent.ACTION_MAIN);
                startMain.addCategory(Intent.CATEGORY_HOME);
                startMain.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(startMain);
            }
        });
        btnConnect.setOnClickListener(v -> {
            if (vpnStart) {
                confirmDisconnect();
            } else {
                prepareVpn();
            }
        });

      /*  currentConnectionLayout.setOnClickListener(v -> {
            if (getActivity() != null) {
                Intent mIntent = new Intent(getActivity(), ServersActivity.class);
                getActivity().startActivityForResult(mIntent, REQUEST_CODE);
            }
        });*/

        currentConnectionLayout.setOnClickListener(v -> {
            if (isNetworkAvailable()) {
                if (getActivity() != null) {
                    Intent mIntent = new Intent(getActivity(), ServersActivity.class);
                    getActivity().startActivityForResult(mIntent, REQUEST_CODE);
                }
            } else {
                showNetworkUnavailableDialog();
            }
        });

        pre.setOnClickListener(v -> {
            if (getActivity() != null) {
                Intent mIntent = new Intent(getActivity(), PremiumActivity.class);
                getActivity().startActivityForResult(mIntent, REQUEST_CODE);
            }
        });

        isServiceRunning();

        VpnStatus.initLogCache(getActivity().getCacheDir());
    }



    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) requireActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    private void showNetworkUnavailableDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(requireActivity());
        builder.setMessage("Network connection is not available");
        builder.setPositiveButton("OK", null);
        AlertDialog dialog = builder.create();
        dialog.show();
    }


    public void confirmDisconnect() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
        builder.setMessage(getActivity().getString(R.string.connection_close_confirm));
        builder.setPositiveButton(getActivity().getString(R.string.yes), (dialog, id) -> stopVpn());
        builder.setNegativeButton(getActivity().getString(R.string.no), (dialog, id) -> {
        });

        AlertDialog dialog = builder.create();
        dialog.show();
    }

    private void prepareVpn() {
        try {
            // For free users, select a random server each time they connect
            if (!ProConfig.isPremium(getContext()) && !vpnStart) {
                selectRandomFreeServer();
            }

            if (vpnStart) {
                switchServer();
            } else {
                if (getInternetStatus()) {
                    // Check VPN permission first
                    Intent intent = VpnService.prepare(getContext());

                    if (intent != null) {
                        Log.d(TAG, "VPN permission required, requesting permission");
                        startActivityForResult(intent, 1);
                    } else {
                        Log.d(TAG, "VPN permission already granted, starting VPN");
                        startVpn();
                        setStatus("connecting");
                    }
                } else {
                    showToast("You have no internet connection!!");
                }
            }
        } catch (SecurityException se) {
            Log.e(TAG, "Security exception during VPN preparation: " + se.getMessage());
            showToast("VPN permission denied. Please grant VPN permission to use this app.");
        } catch (Exception e) {
            Log.e(TAG, "Error during VPN preparation: " + e.getMessage());
            e.printStackTrace();
            showToast("An error occurred: " + e.getMessage());
        }
    }

    // Select a random free server from the available servers
    private void selectRandomFreeServer() {
        try {
            JSONArray jsonArray = new JSONArray(Const.SERVERS);

            // Create a list of free servers (type="1")
            java.util.List<JSONObject> freeServers = new java.util.ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject serverObj = jsonArray.getJSONObject(i);
                if (serverObj.getString("type").contains("1")) {
                    freeServers.add(serverObj);
                }
            }

            // Select a random server from the free servers list
            if (freeServers.size() > 0) {
                int randomIndex = new java.util.Random().nextInt(freeServers.size());
                JSONObject object = freeServers.get(randomIndex);

                server = new Server(object.getString("name"),
                        Utils.imgUrl("flag/", object.getString("flagURL")),
                        object.getString("configFile"),
                        object.getString("username"),
                        object.getString("password"));

                // Update MainActivity's default server
                ((MainActivity) getActivity()).defaultServer.setValue(server);

                // Log the random server info
                Log.d(TAG, "Selected random free server: " + object.getString("name"));
                Log.d(TAG, "Server index: " + (randomIndex + 1) + " of " + freeServers.size() + " available");

                // Update UI
                countryName.setText(server.getCountry());
                updateCurrentVipServerIcon(server.getFlagUrl());

                // Save the server to preferences so it persists across app restarts
                pref.saveServer(server);

//                showToast("Connected to " + server.getCountry());
            }
        } catch (JSONException e) {
            e.printStackTrace();
            Log.e(TAG, "Error selecting random server: " + e.getMessage());
        }
    }

    private void switchServer() {
        try {
            if (vpnStart) {
                if (stopVpn()) {
                    showToast("Disconnecting from the current server...");
                    startVpn();
                    setStatus("connecting to the new server...");
                } else {
                    showToast("Failed to disconnect from the current server.");
                }
            } else {
                showToast("VPN is not currently started.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            showToast("An error occurred: " + e.getMessage());
        }
    }

    public boolean stopVpn() {
        try {
            Log.d(TAG, "Stopping VPN connection - checking all implementations");

            // CRITICAL FIX: Stop BOTH implementations since VpnService V2 might fallback to native OpenVPN
            // This handles the case where VpnService V2 transitions to native OpenVPN during connection

            Log.d(TAG, "Stopping VpnService V2 implementation");
            OpenVpnApiV2.stopVpn(getContext());

            Log.d(TAG, "Stopping native OpenVPN implementation");
            try {
                // Stop the legacy native binary implementation
                if (vpnThread != null) {
                    vpnThread.stop();
                }
                // Also use the OpenVpnApi to stop native implementation
                de.blinkt.openvpn.OpenVpnApi.stopVpn(getContext());
            } catch (Exception e) {
                Log.w(TAG, "Error stopping native OpenVPN (may not be running)", e);
            }

            // CRITICAL FIX: Force cleanup of all VPN services
            forceCleanupAllVpnServices();

            // Update UI state immediately
            vpnStart = false;
            setStatus("DISCONNECTED");

            // Update button animation
            buttonAnim("l");

            // Update protection status
            protectionStatus.setText("Disconnecting...");
            protectionStatus.setTextColor(getResources().getColor(R.color.white));

            Log.d(TAG, "VPN stopped successfully - all implementations");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error stopping VPN", e);
            e.printStackTrace();

            // Even if there's an error, update the UI to reflect disconnected state
            vpnStart = false;
            setStatus("DISCONNECTED");

            // CRITICAL FIX: Force cleanup on error
            forceCleanupAllVpnServices();
        }
        return false;
    }

    /**
     * CRITICAL FIX: Force cleanup of ALL VPN services and notifications
     * This handles both VpnService V2 and native OpenVPN implementations
     */
    private void forceCleanupAllVpnServices() {
        try {
            Log.d(TAG, "Performing comprehensive force cleanup of ALL VPN services");

            // Immediate cleanup attempt
            performVpnCleanup();

            // Schedule additional cleanup after a delay to ensure everything is stopped
            new Handler().postDelayed(() -> {
                performVpnCleanup();
                Log.d(TAG, "Delayed force cleanup completed");
            }, 2000); // Wait 2 seconds before additional cleanup

            // Final cleanup after 5 seconds for stubborn services
            new Handler().postDelayed(() -> {
                performVpnCleanup();
                Log.d(TAG, "Final force cleanup completed");
            }, 5000); // Wait 5 seconds for final cleanup

        } catch (Exception e) {
            Log.e(TAG, "Error in force cleanup setup", e);
        }
    }

    /**
     * Perform actual VPN cleanup operations
     */
    private void performVpnCleanup() {
        try {
            Log.d(TAG, "Executing VPN cleanup operations");

            // Force stop OpenVPNServiceV2
            Intent stopV2Intent = new Intent(getContext(), de.blinkt.openvpn.core.OpenVPNServiceV2.class);
            getContext().stopService(stopV2Intent);

            // Force stop OpenVPNService (native implementation)
            Intent stopV1Intent = new Intent(getContext(), de.blinkt.openvpn.core.OpenVPNService.class);
            getContext().stopService(stopV1Intent);

            // Clear ALL VPN-related notifications
            android.app.NotificationManager notificationManager =
                (android.app.NotificationManager) getContext().getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                // Cancel all possible VPN notification IDs
                notificationManager.cancel(1001); // OpenVPNServiceV2
                notificationManager.cancel(1000); // OpenVPNService
                notificationManager.cancel(1);    // Generic VPN notification
                notificationManager.cancel(2);    // Alternative VPN notification

                // Cancel notifications by tag if they exist
                try {
                    notificationManager.cancel("vpn_notification", 1001);
                    notificationManager.cancel("openvpn_notification", 1000);
                } catch (Exception e) {
                    Log.w(TAG, "Error cancelling tagged notifications", e);
                }

                Log.d(TAG, "Cleared all VPN notifications");
            }

            // Additional cleanup: Try to clear Android's built-in VPN notification
            try {
                // This attempts to clear the system VPN notification
                notificationManager.cancel("VpnService", 17040540); // Android system VPN notification
            } catch (Exception e) {
                Log.w(TAG, "Could not clear system VPN notification", e);
            }

            Log.d(TAG, "VPN cleanup operations completed");
        } catch (Exception e) {
            Log.w(TAG, "Error during VPN cleanup operations", e);
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    private void forceCleanupVpnServices() {
        forceCleanupAllVpnServices();
    }



    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == 1) { // VPN permission request
            if (resultCode == RESULT_OK) {
                Log.d(TAG, "VPN permission granted, starting VPN");
                try {
                    startVpn();
                    setStatus("connecting");
                } catch (Exception e) {
                    Log.e(TAG, "Error starting VPN after permission grant: " + e.getMessage());
                    showToast("Failed to start VPN: " + e.getMessage());
                }
            } else {
                Log.w(TAG, "VPN permission denied by user");
                showToast("VPN permission is required to use this app");
                setStatus("disconnected");
            }
        }
    }

    public boolean getInternetStatus() {
        return connection.netCheck(getActivity());
    }

    public void isServiceRunning() {
        setStatus(vpnService.getStatus());
    }

    // private void startVpn() {
    //                         OpenVpnApi.startVpn(getContext(), server.getOvpn(), server.getCountry(), server.getOvpnUserName(), server.getOvpnUserPassword());
    //                         Log.e(TAG, "Error starting VPN after ad: " + e.getMessage());
    //                         e.printStackTrace();
    //                     }
    //                 }

    //                 @Override
    //                 public void failed() {
    //                     try {
    //                         Log.d(TAG, "Rewarded ad failed to show, connecting VPN directly");
    //                         OpenVpnApi.startVpn(getContext(), server.getOvpn(), server.getCountry(), server.getOvpnUserName(), server.getOvpnUserPassword());
    //                         binding.logTv.setText("Connecting...");
    //                         binding.logTv.setTextColor(getResources().getColor(R.color.white));
    //                         Log.e(TAG, "Error starting VPN after ad failed: " + e.getMessage());
    //                         e.printStackTrace();
    //                     }
    //                 }
    //             }, false);
    //         }, 2000); // Give 2 seconds for ad to load
    //     } else {
    //         Log.d(TAG, "Ads are disabled, connecting VPN directly");
    //         try {
    //             vpnStart = true;
    //         } catch (RemoteException e) {
    //             Log.e(TAG, "Error starting VPN: " + e.getMessage());
    //             e.printStackTrace();
    //         }
    //     }
    // }

    private void startVpn() {
        try {
            // Log server information when connecting
            Log.d(TAG, "Connecting to server: " + server.getCountry());
            Log.d(TAG, "Is Default Server: " + (server.equals(pref.getServer())));

            // Check if we should use the new VpnService-based implementation
            boolean useVpnServiceV2 = shouldUseVpnServiceV2();

            if (useVpnServiceV2) {
                Log.d(TAG, "Using VpnService-based implementation (V2) - Compatible with Android 10+");

                // Use the new VpnService-based implementation
                OpenVpnApiV2.startVpn(getContext(), server.getOvpn(), server.getCountry(),
                        server.getOvpnUserName(), server.getOvpnUserPassword());
            } else {
                Log.d(TAG, "Using legacy native binary implementation (V1)");

                // Use the original native binary implementation
                OpenVpnApi.startVpn(getContext(), server.getOvpn(), server.getCountry(),
                        server.getOvpnUserName(), server.getOvpnUserPassword());
            }

            protectionStatus.setText("Connecting...");
            protectionStatus.setTextColor(getResources().getColor(R.color.white));
            vpnStart = true;

            // Removed reward ad from here to prevent overlapping with interstitial ads
            // Ads will be shown in proper sequence when VPN status changes to CONNECTED

        } catch (RemoteException e) {
            Log.e(TAG, "Error starting VPN: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Determine whether to use VpnService-based implementation
     * Returns true for Android 10+ or if explicitly enabled in preferences
     */
    private boolean shouldUseVpnServiceV2() {
        // Always use VpnService V2 for Android 10+ to avoid SELinux issues
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            Log.d(TAG, "Android 10+ detected, using VpnService V2 to avoid SELinux restrictions");
            return true;
        }

        // For older Android versions, check user preference (default to V2 for better compatibility)
        SharedPreferences prefs = getContext().getSharedPreferences("VPNPreference", Context.MODE_PRIVATE);
        boolean useV2 = prefs.getBoolean("use_vpnservice_v2", true);
        Log.d(TAG, "Android version: " + Build.VERSION.SDK_INT + ", VpnService V2 preference: " + useV2);
        return useV2;
    }


    public void setStatus(String connectionState) {
        if (connectionState != null) {
            switch (connectionState) {
                case "DISCONNECTED":
                    vpnStart = false;
                    vpnService.setDefaultStatus();
                    protectionStatus.setText("You are not protected");
                    protectionStatus.setTextColor(getResources().getColor(R.color.white));
                    durationTv.setText("00:00:00");
                    durationTv.setVisibility(View.GONE);
                    statsLayout.setVisibility(View.GONE);
                    stopTimer();
                    stopSpeedUpdate();
                    buttonAnim("l");
                    new Handler().postDelayed(() -> getIP(), 1000);
                    renewButtonLayout.setVisibility(ProConfig.isPremium(getContext()) ? View.GONE : View.GONE);
                    logTv.setVisibility(View.VISIBLE);

                    // CRITICAL FIX: Clear VPN state when disconnected
                    if (!isRestoringState) {
                        pref.saveVpnConnectionState(false);
                        pref.clearVpnState();
                        timerStartTime = 0;
                        // Reset ad throttling when disconnected to allow ads on next connection
                        resetAdThrottling();
                    }

                    // CRITICAL FIX: Clear notification timer when disconnected
                    clearVpnNotificationTimer();
                    break;

                case "CONNECTED":
                    vpnStart = true;
                    protectionStatus.setText("You are now protected");
                    protectionStatus.setTextColor(getResources().getColor(R.color.white));

                    // CRITICAL FIX: Only show ads if NOT restoring state to prevent infinite loop
                    if (!isRestoringState) {
                        showInter();
                    } else {
                        Log.d(TAG, "Skipping ads during state restoration to prevent infinite loop");
                    }

                    buttonAnim("c");

                    // CRITICAL FIX: Check if timer is already running to prevent reset after reward ads
                    boolean isTimerAlreadyRunning = (timerRunnable != null && remainingTimeInSeconds > 0);
                    Log.d(TAG, "TIMER DEBUG: setStatus CONNECTED - isTimerAlreadyRunning = " + isTimerAlreadyRunning);
                    Log.d(TAG, "TIMER DEBUG: setStatus CONNECTED - remainingTimeInSeconds = " + remainingTimeInSeconds);
                    Log.d(TAG, "TIMER DEBUG: setStatus CONNECTED - isRestoringState = " + isRestoringState);

                    if (isTimerAlreadyRunning && !isRestoringState) {
                        Log.d(TAG, "TIMER DEBUG: Timer already running, skipping startTimer() to prevent reset");
                        // Timer is already running, don't restart it to prevent overwriting extensions
                        updateTimerDisplay(); // Just update the display
                    } else {
                        Log.d(TAG, "TIMER DEBUG: Starting timer - isRenewal = " + isTimerAlreadyRunning);
                        startTimer(isTimerAlreadyRunning); // Pass true if timer was already running (renewal)
                    }

                    startSpeedUpdate();
                    new Handler().postDelayed(() -> getIP(), 2000);
                    durationTv.setVisibility(View.VISIBLE);
                    statsLayout.setVisibility(View.VISIBLE);
                    // Only show renew button for non-premium users
                    renewButtonLayout.setVisibility(ProConfig.isPremium(getContext()) ? View.GONE : View.VISIBLE);
                    logTv.setVisibility(View.GONE);

                    // CRITICAL FIX: Save VPN state when connected
                    if (!isRestoringState) {
                        pref.saveVpnConnectionState(true);
                        if (server != null) {
                            pref.saveConnectedServerInfo(server.getCountry(), server.getFlagUrl());
                        }
                    }
                    break;

                case "WAIT":
                    protectionStatus.setText("Please wait");
                    protectionStatus.setTextColor(getResources().getColor(R.color.white));
                    break;

                case "AUTH":
                    protectionStatus.setText("Please wait");
                    protectionStatus.setTextColor(getResources().getColor(R.color.white));
                    break;

                case "RECONNECTING":
                    protectionStatus.setText("Please wait");
                    protectionStatus.setTextColor(getResources().getColor(R.color.white));
                    break;

                case "NONETWORK":
                    protectionStatus.setText("No network connection");
                    protectionStatus.setTextColor(getResources().getColor(R.color.white));
                    break;

                case "PERMISSION_REVOKED":
                    vpnStart = false;
                    protectionStatus.setText("VPN permission revoked");
                    protectionStatus.setTextColor(getResources().getColor(R.color.white));
                    buttonAnim("l");
                    showToast("VPN permission was revoked. Please reconnect to grant permission again.");
                    break;
            }
        }
    }

    private void startSpeedUpdate() {
        // Stop any existing runnable to prevent multiple updates
        if (speedUpdateRunnable != null) {
            speedUpdateHandler.removeCallbacks(speedUpdateRunnable);
        }

        speedUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                if (vpnStart) {
                    // Trigger getIP to update byte stats
                    getIP();

                    // Schedule next update in 1 second
                    speedUpdateHandler.postDelayed(this, 1000);
                }
            }
        };

        // Start the first update
        speedUpdateHandler.post(speedUpdateRunnable);
    }

    private void stopSpeedUpdate() {
        if (speedUpdateRunnable != null) {
            speedUpdateHandler.removeCallbacks(speedUpdateRunnable);
            speedUpdateRunnable = null;
        }

        // Reset speed TextViews when stopping
        requireActivity().runOnUiThread(() -> {
            byteInTv.setText("0 KB/s");
            byteOutTv.setText("0 KB/s");
        });
    }

    private void updateByteStats(String byteIn, String byteOut) {
        if (byteIn == null) byteIn = "0.0";
        if (byteOut == null) byteOut = "0.0";

        // Update byte stats
        byteInTv.setText(byteIn.split("-")[0]);
        byteOutTv.setText(byteOut.split("-")[0]);
    }

//    private void getIP() {
//        RequestQueue queue = Volley.newRequestQueue(getActivity());
//        StringRequest request = new StringRequest(Request.Method.GET, Const.ip,
//                response -> {
//                    try {
//                        JSONObject jsonObject = new JSONObject(response);
//                        String ip = jsonObject.getString("ip");
//                        String byteIn = jsonObject.optString("byteIn", "0.0");
//                        String byteOut = jsonObject.optString("byteOut", "0.0");
//
//                        // Update UI on main thread
//                        requireActivity().runOnUiThread(() -> {
//                            ipTv.setText(ip);
//                            updateByteStats(byteIn, byteOut);
//                        });
//                    } catch (JSONException e) {
//                        e.printStackTrace();
//                    }
//                },
//                error -> {
//                    // Handle error
//                });
//
//        queue.add(request);
//    }

    private void getIP() {
        // Ensure the fragment is attached before accessing the Activity
        if (!isAdded()) {
            Log.e("MainFragment", "Fragment not attached. Cannot fetch IP.");
            return;
        }

        Context context = requireContext().getApplicationContext(); // safe context
        RequestQueue queue = Volley.newRequestQueue(context);

        StringRequest request = new StringRequest(Request.Method.GET, Const.ip,
                response -> {
                    try {
                        JSONObject jsonObject = new JSONObject(response);
                        String ip = jsonObject.optString("ip", "N/A");
                        String byteIn = jsonObject.optString("byteIn", "0.0");
                        String byteOut = jsonObject.optString("byteOut", "0.0");

                        // Update UI safely on the main thread
                        if (isAdded()) {
                            requireActivity().runOnUiThread(() -> {
                                ipTv.setText(ip);
                                updateByteStats(byteIn, byteOut);
                            });
                        }

                    } catch (JSONException e) {
                        Log.e("MainFragment", "IP JSON parsing error: " + e.getMessage());
                    }
                },
                error -> {
                    Log.e("MainFragment", "Volley error: " + error.toString());
                });

        queue.add(request);
    }


    public void updateConnectionStatus(String duration, String byteIn, String byteOut) {
        if (duration != null && !duration.isEmpty()) {
//            durationTv.setText(duration);
        }

        updateByteStats(byteIn, byteOut);
    }

    private void buttonAnim(String anim) {
        if (anim.equals("c")) {
            btnConnect.setAnimation(R.raw.clottie);
            btnConnect.playAnimation();
            btnConnect.loop(true);
        } else {
            btnConnect.loop(false);
        }
    }

    BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                setStatus(intent.getStringExtra("state"));
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                String duration = intent.getStringExtra("duration");
                String lastPacketReceive = intent.getStringExtra("lastPacketReceive");
                String byteIn = intent.getStringExtra("byteIn");
                String byteOut = intent.getStringExtra("byteOut");

                if (duration == null) duration = "00:00:00";
                if (lastPacketReceive == null) lastPacketReceive = "0";
                if (byteIn == null) byteIn = "00:00:00 ";
                if (byteOut == null) byteOut = "00:00:00 ";
                updateConnectionStatus(duration, byteIn, byteOut);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    // New broadcast receiver for traffic statistics from VpnService V2
    BroadcastReceiver trafficStatsReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                long uploaded = intent.getLongExtra("uploaded", 0);
                long downloaded = intent.getLongExtra("downloaded", 0);

                // Format bytes to human readable format
                String uploadStr = formatBytes(uploaded);
                String downloadStr = formatBytes(downloaded);

                // Update UI with real traffic statistics
                updateConnectionStatus("", downloadStr, uploadStr);

                Log.d(TAG, "Traffic stats updated - Up: " + uploadStr + ", Down: " + downloadStr);
            } catch (Exception e) {
                Log.e(TAG, "Error processing traffic stats", e);
            }
        }
    };

    // CRITICAL FIX: Broadcast receiver for native OpenVPN fallback
    BroadcastReceiver nativeOpenVpnReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                Log.d(TAG, "Received native OpenVPN startup request for full protocol support");

                String config = intent.getStringExtra("config");
                String username = intent.getStringExtra("username");
                String password = intent.getStringExtra("password");

                if (config != null) {
                    Log.d(TAG, "Transitioning to native OpenVPN for proper server connection");

                    // Update UI to show transition
                    setStatus("connecting");
                    protectionStatus.setText("Connecting via Native OpenVPN...");

                    // Stop VpnService V2 first (it should already be stopping itself)
                    try {
                        OpenVpnApiV2.stopVpn(getContext());
                    } catch (Exception e) {
                        Log.w(TAG, "VpnService V2 already stopped or stopping", e);
                    }

                    // Start native OpenVPN using the original API
                    new Handler().postDelayed(() -> {
                        try {
                            Log.d(TAG, "Starting native OpenVPN implementation for full server connection");
                            OpenVpnApi.startVpn(getContext(), config, server.getCountry(), username, password);
                            Log.d(TAG, "Native OpenVPN started successfully - should establish real server connection");
                        } catch (RemoteException e) {
                            Log.e(TAG, "Error starting native OpenVPN", e);
                            showToast("Failed to start native OpenVPN: " + e.getMessage());
                            setStatus("DISCONNECTED");
                            protectionStatus.setText("Disconnected");
                        }
                    }, 1000); // Give 1 second for VpnService V2 to fully stop
                } else {
                    Log.e(TAG, "No configuration provided for native OpenVPN");
                    showToast("Invalid configuration for native OpenVPN");
                    setStatus("DISCONNECTED");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in native OpenVPN receiver", e);
                showToast("Error starting native OpenVPN: " + e.getMessage());
                setStatus("DISCONNECTED");
            }
        }
    };

    public void showToast(String message) {
        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
    }

    /**
     * Format bytes to human readable format
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    public void updateCurrentVipServerIcon(String serverIcon) {
        Glide.with(getActivity()).load(serverIcon).into(selectedServerIcon);
    }

    @Override
    public void newServer(Server server) {
        this.server = server;
        if (vpnStart) {
            stopVpn();
        }
        prepareVpn();
    }

    private void startTimer() {
        startTimer(false); // Default: not a renewal
    }

    private void startTimer(boolean isRenewal) {
        // CRITICAL FIX: Additional safeguard - if we have remaining time and timer is running, treat as renewal
        if (!isRestoringState && remainingTimeInSeconds > 0 && timerRunnable != null) {
            Log.d(TAG, "TIMER DEBUG: Detected active timer with remaining time, forcing renewal mode");
            isRenewal = true;
        }

        // CRITICAL FIX: Clear any old timer state when starting fresh (not during restoration or renewal)
        if (!isRestoringState && !isRenewal) {
            Log.d(TAG, "TIMER DEBUG: Starting fresh timer, clearing old state");
            remainingTimeInSeconds = 0; // Reset to ensure fresh initialization
            timerStartTime = 0;
        } else if (isRenewal) {
            Log.d(TAG, "TIMER DEBUG: Starting timer renewal, keeping existing time: " + remainingTimeInSeconds);
        }

        // CRITICAL FIX: Save timer start time for state persistence
        if (timerStartTime == 0) {
            timerStartTime = System.currentTimeMillis();
            Log.d(TAG, "TIMER DEBUG: Set new timerStartTime = " + timerStartTime);
        } else if (isRenewal) {
            Log.d(TAG, "TIMER DEBUG: Keeping existing timerStartTime for renewal = " + timerStartTime);
        }

        // Check if user is premium
        if (ProConfig.isPremium(getContext())) {
            // For premium users, just start duration tracking without countdown
            if (timerRunnable != null) {
                timerHandler.removeCallbacks(timerRunnable);
                timerRunnable = null;
            }

            timerRunnable = new Runnable() {
                @Override
                public void run() {
                    // Calculate elapsed time from start
                    long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                    int durationSeconds = (int) (elapsedMillis / 1000);

                    int hours = durationSeconds / 3600;
                    int minutes = (durationSeconds % 3600) / 60;
                    int seconds = durationSeconds % 60;
                    String timeStr = String.format("%02d:%02d:%02d", hours, minutes, seconds);
                    durationTv.setText(timeStr);

                    // CRITICAL FIX: Save timer state periodically
                    if (!isRestoringState) {
                        pref.saveVpnTimerState(timerStartTime, durationSeconds, true, 0);
                    }

                    // CRITICAL FIX: Update VPN notification with timer info for premium users
                    updateVpnNotificationTimer();

                    timerHandler.postDelayed(this, 1000);
                }
            };
            timerHandler.post(timerRunnable);

            // Start background timer service for premium users AFTER initialization
            startBackgroundTimerService();
            return;
        }

        // For non-premium users, use the countdown timer
        Log.d(TAG, "TIMER DEBUG: Checking timer initialization for non-premium user");
        Log.d(TAG, "TIMER DEBUG: remainingTimeInSeconds = " + remainingTimeInSeconds);
        Log.d(TAG, "TIMER DEBUG: AdsHelper.reward_time = " + AdsHelper.reward_time);
        Log.d(TAG, "TIMER DEBUG: isRenewal = " + isRenewal);

        // Only initialize timer if it's not a renewal and remainingTimeInSeconds is 0
        if (!isRenewal && remainingTimeInSeconds == 0 && AdsHelper.reward_time != null && !AdsHelper.reward_time.isEmpty()) {
            try {
                int minutes = Integer.parseInt(AdsHelper.reward_time);
                remainingTimeInSeconds = minutes * 60;
                Log.d(TAG, "TIMER DEBUG: Initialized remainingTimeInSeconds = " + remainingTimeInSeconds + " (" + minutes + " minutes)");
            } catch (NumberFormatException e) {
                e.printStackTrace();
                Log.e(TAG, "TIMER DEBUG: Failed to parse reward_time: " + AdsHelper.reward_time);
                return;
            }
        } else if (!isRenewal && (AdsHelper.reward_time == null || AdsHelper.reward_time.isEmpty())) {
            Log.e(TAG, "TIMER DEBUG: AdsHelper.reward_time is null or empty! This will cause timer issues.");
            Log.e(TAG, "TIMER DEBUG: Using fallback value of 30 minutes");
            remainingTimeInSeconds = 30 * 60; // Fallback to 30 minutes
        } else if (isRenewal) {
            Log.d(TAG, "TIMER DEBUG: Skipping timer initialization for renewal, keeping existing time: " + remainingTimeInSeconds);
        }

        if (remainingTimeInSeconds <= 0) {
            Log.e(TAG, "TIMER DEBUG: remainingTimeInSeconds is <= 0: " + remainingTimeInSeconds);
            return;
        }

        Log.d(TAG, "TIMER DEBUG: Starting timer with " + remainingTimeInSeconds + " seconds remaining");

        if (timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
            timerRunnable = null;
        }

        timerRunnable = new Runnable() {
            @Override
            public void run() {
                if (remainingTimeInSeconds > 0) {
                    remainingTimeInSeconds--;
                    updateTimerDisplay();

                    // CRITICAL FIX: Save timer state for non-premium users
                    if (!isRestoringState) {
                        long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                        int durationSeconds = (int) (elapsedMillis / 1000);
                        pref.saveVpnTimerState(timerStartTime, durationSeconds, false, remainingTimeInSeconds);
                    }

                    // CRITICAL FIX: Update VPN notification with timer info for free users
                    updateVpnNotificationTimer();

                    // Check for 1-minute warning
                    if (remainingTimeInSeconds == 60) {
                        showOneMinuteWarningNotification();
                    }

                    timerHandler.postDelayed(this, 1000);
                } else {
                    stopVpn();
                    showToast("Time limit reached. VPN disconnected.");
                    stopTimer();
                }
            }
        };
        timerHandler.post(timerRunnable);
        updateTimerDisplay();

        // CRITICAL FIX: Start background timer service AFTER timer is properly initialized
        // Only start if this is not a renewal or if we're restoring state
        if (!isRenewal || isRestoringState) {
            Log.d(TAG, "TIMER DEBUG: Starting background service with " + remainingTimeInSeconds + " seconds");
            startBackgroundTimerService();
        } else {
            Log.d(TAG, "TIMER DEBUG: Skipping background service restart for renewal to prevent timer reset");
        }
    }

    private void stopTimer() {
        stopTimer(true); // Default: stop background service
    }

    private void stopTimer(boolean stopBackgroundService) {
        if (timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
            timerRunnable = null;
        }
        if (!ProConfig.isPremium(getContext())) {
            remainingTimeInSeconds = 0;
        }
        durationTv.setText("00:00:00");

        // CRITICAL FIX: Reset timer start time when timer is stopped
        timerStartTime = 0;

        // CRITICAL FIX: Clear notification timer when timer stops
        clearVpnNotificationTimer();

        // Stop background timer service only when explicitly requested
        if (stopBackgroundService) {
            stopBackgroundTimerService();
            Log.d(TAG, "Timer stopped with background service termination");
        } else {
            Log.d(TAG, "Timer stopped but background service continues running");
        }
    }

    /**
     * Start background timer service to keep timer running when app is closed
     */
    private void startBackgroundTimerService() {
        try {
            boolean isPremium = ProConfig.isPremium(getContext());

            Log.d(TAG, "TIMER DEBUG: startBackgroundTimerService called");
            Log.d(TAG, "TIMER DEBUG: timerStartTime = " + timerStartTime);
            Log.d(TAG, "TIMER DEBUG: remainingTimeInSeconds = " + remainingTimeInSeconds);
            Log.d(TAG, "TIMER DEBUG: isPremium = " + isPremium);

            Intent serviceIntent = new Intent(getContext(), VpnTimerService.class);
            serviceIntent.putExtra("timer_start_time", timerStartTime);
            serviceIntent.putExtra("remaining_time", remainingTimeInSeconds);
            serviceIntent.putExtra("is_premium", isPremium);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getContext().startForegroundService(serviceIntent);
            } else {
                getContext().startService(serviceIntent);
            }
            Log.d(TAG, "Background timer service started with " + remainingTimeInSeconds + " seconds remaining");
        } catch (Exception e) {
            Log.e(TAG, "Error starting background timer service", e);
        }
    }

    /**
     * Stop background timer service
     */
    private void stopBackgroundTimerService() {
        try {
            Intent serviceIntent = new Intent(getContext(), VpnTimerService.class);
            getContext().stopService(serviceIntent);
            Log.d(TAG, "Background timer service stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping background timer service", e);
        }
    }

    /**
     * Update background timer service with new timer values (for time extensions)
     */
    private void updateBackgroundServiceTimer() {
        try {
            boolean isPremium = ProConfig.isPremium(getContext());

            Log.d(TAG, "TIMER DEBUG: updateBackgroundServiceTimer called");
            Log.d(TAG, "TIMER DEBUG: Updating service with remainingTimeInSeconds = " + remainingTimeInSeconds);

            Intent serviceIntent = new Intent(getContext(), VpnTimerService.class);
            serviceIntent.putExtra("timer_start_time", timerStartTime);
            serviceIntent.putExtra("remaining_time", remainingTimeInSeconds);
            serviceIntent.putExtra("is_premium", isPremium);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getContext().startForegroundService(serviceIntent);
            } else {
                getContext().startService(serviceIntent);
            }
            Log.d(TAG, "Background timer service updated with new time: " + remainingTimeInSeconds + " seconds");
        } catch (Exception e) {
            Log.e(TAG, "Error updating background timer service", e);
        }
    }

    /**
     * Show 1-minute warning notification
     */
    private void showOneMinuteWarningNotification() {
        try {
            NotificationManager notificationManager = (NotificationManager) getContext().getSystemService(Context.NOTIFICATION_SERVICE);

            // Create notification channel for Android O+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel channel = new NotificationChannel(
                    "vpn_warning_channel",
                    "VPN Warning",
                    NotificationManager.IMPORTANCE_HIGH
                );
                channel.setDescription("VPN time warning notifications");
                channel.enableVibration(true);
                channel.setLightColor(android.graphics.Color.RED);
                notificationManager.createNotificationChannel(channel);
            }

            // Create intent for when notification is tapped
            Intent notificationIntent = new Intent(getContext(), VpnWarningActivity.class);
            notificationIntent.putExtra("remaining_time", remainingTimeInSeconds);
            notificationIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

            PendingIntent pendingIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivity(getContext(), 0, notificationIntent, PendingIntent.FLAG_MUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivity(getContext(), 0, notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT);
            }

            // Build notification
            Notification.Builder builder;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder = new Notification.Builder(getContext(), "vpn_warning_channel");
            } else {
                builder = new Notification.Builder(getContext());
            }

            builder.setSmallIcon(R.drawable.fivegsmartvpn)
                   .setContentTitle("VPN Time Warning")
                   .setContentText("Only 1 minute remaining! Tap to extend time.")
                   .setAutoCancel(true)
                   .setContentIntent(pendingIntent)
                   .setPriority(Notification.PRIORITY_HIGH)
                   .setDefaults(Notification.DEFAULT_ALL);

            notificationManager.notify(9999, builder.build());
            Log.d(TAG, "1-minute warning notification shown");
        } catch (Exception e) {
            Log.e(TAG, "Error showing warning notification", e);
        }
    }

//    private void updateTimerDisplay() {
//        if (ProConfig.isPremium(getContext())) {
//            return; // Don't update display for premium users as it's handled in the timer runnable
//        }
//        int minutes = remainingTimeInSeconds / 60;
//        int seconds = remainingTimeInSeconds % 60;
//        String timeStr = String.format("%02d:%02d:00", minutes, seconds);
//        durationTv.setText(timeStr);
//    }

    private void updateTimerDisplay() {
        if (ProConfig.isPremium(getContext())) {
            return; // Don't update display for premium users as it's handled in the timer runnable
        }

        int hours = remainingTimeInSeconds / 3600; // Calculate hours
        int minutes = (remainingTimeInSeconds % 3600) / 60; // Calculate minutes
        int seconds = remainingTimeInSeconds % 60; // Calculate seconds

        String timeStr = String.format("%02d:%02d:%02d", hours, minutes, seconds); // Format as HH:MM:SS
        durationTv.setText(timeStr);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // Unregister broadcast receivers
        try {
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(broadcastReceiver);
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(trafficStatsReceiver);
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(timerUpdateReceiver);
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(timerExpiredReceiver);
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(timeExtendedReceiver);
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(timerExtendedReceiver);
            getActivity().unregisterReceiver(nativeOpenVpnReceiver);
        } catch (Exception e) {
            Log.e(TAG, "Error unregistering broadcast receivers", e);
        }

        // Stop UI timer but keep background service running when app is closed
        stopTimer(false); // Don't stop background service
    }

    /**
     * CRITICAL FIX: Detect and restore VPN state on app startup
     */
    private void detectAndRestoreVpnState() {
        try {
            // Mark that we've performed initial state detection
            hasInitialStateDetection = true;

            // Check if VPN was connected when app was last closed
            boolean wasConnected = pref.getVpnConnectionState();

            if (wasConnected) {
                Log.d(TAG, "Detected previous VPN connection, attempting to restore state");
                isRestoringState = true;

                // Check if VPN service is actually still running
                boolean isVpnServiceRunning = isVpnServiceRunning();

                if (isVpnServiceRunning) {
                    Log.d(TAG, "VPN service is still running, restoring UI state");

                    // Restore VPN state
                    vpnStart = true;

                    // Restore server info if available
                    String connectedCountry = pref.getConnectedServerCountry();
                    String connectedFlag = pref.getConnectedServerFlag();

                    if (!connectedCountry.isEmpty() && server != null) {
                        // Update UI with connected server info
                        countryName.setText(connectedCountry);
                        if (!connectedFlag.isEmpty()) {
                            updateCurrentVipServerIcon(connectedFlag);
                        }
                    }

                    // Restore timer state
                    restoreTimerState();

                    // Update UI to connected state
                    setStatus("CONNECTED");

                    Log.d(TAG, "VPN state restored successfully");
                } else {
                    Log.d(TAG, "VPN service not running, clearing saved state");
                    // VPN service is not running, clear the saved state
                    pref.clearVpnState();
                    vpnStart = false;
                    setStatus("DISCONNECTED");
                }

                isRestoringState = false;
            } else {
                Log.d(TAG, "No previous VPN connection detected");
                vpnStart = false;
                setStatus("DISCONNECTED");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error detecting VPN state", e);
            isRestoringState = false;
            vpnStart = false;
            setStatus("DISCONNECTED");
        }
    }

    /**
     * Check if VPN service is currently running
     */
    private boolean isVpnServiceRunning() {
        try {
            // Check if any VPN is currently active
            android.net.VpnService vpnService = null;

            // Try to get VPN status from OpenVPN service
            String currentStatus = OpenVPNService.getStatus();
            if (currentStatus != null && !currentStatus.isEmpty() &&
                (currentStatus.equals("CONNECTED") || currentStatus.equals("CONNECTING"))) {
                return true;
            }

            // Additional check using VpnService.prepare()
            Intent intent = android.net.VpnService.prepare(getContext());
            // If intent is null, VPN permission is already granted and might be active
            // This is not a definitive check but helps in detection

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking VPN service status", e);
            return false;
        }
    }

    /**
     * Restore timer state from saved preferences
     */
    private void restoreTimerState() {
        try {
            long savedStartTime = pref.getVpnTimerStartTime();
            boolean wasPremiumSession = pref.getVpnIsPremiumSession();
            int savedRemainingTime = pref.getVpnRemainingTime();

            if (savedStartTime > 0) {
                timerStartTime = savedStartTime;

                if (wasPremiumSession) {
                    // For premium users, calculate elapsed time and continue
                    long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                    int elapsedSeconds = (int) (elapsedMillis / 1000);

                    // Update timer display immediately
                    int hours = elapsedSeconds / 3600;
                    int minutes = (elapsedSeconds % 3600) / 60;
                    int seconds = elapsedSeconds % 60;
                    String timeStr = String.format("%02d:%02d:%02d", hours, minutes, seconds);
                    durationTv.setText(timeStr);

                    Log.d(TAG, "Restored premium timer state: " + timeStr);
                } else {
                    // For non-premium users, restore remaining time
                    if (savedRemainingTime > 0) {
                        remainingTimeInSeconds = savedRemainingTime;
                        updateTimerDisplay();
                        Log.d(TAG, "Restored non-premium timer state: " + remainingTimeInSeconds + " seconds remaining");
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error restoring timer state", e);
        }
    }

    /**
     * CRITICAL FIX: Ad throttling methods to prevent infinite loops
     */
    private boolean canShowAds() {
        long currentTime = System.currentTimeMillis();

        // Don't show ads if we're restoring state
        if (isRestoringState) {
            Log.d(TAG, "Cannot show ads - currently restoring state");
            return false;
        }

        // Don't show ads if we've already shown them this session and cooldown hasn't passed
        if (hasShownAdsThisSession && (currentTime - lastAdShownTime) < AD_COOLDOWN_PERIOD) {
            Log.d(TAG, "Cannot show ads - cooldown period active. Time remaining: " +
                  ((AD_COOLDOWN_PERIOD - (currentTime - lastAdShownTime)) / 1000) + " seconds");
            return false;
        }

        return true;
    }

    private void markAdShown() {
        hasShownAdsThisSession = true;
        lastAdShownTime = System.currentTimeMillis();
        Log.d(TAG, "Ad shown - marked timestamp: " + lastAdShownTime);
    }

    private void resetAdThrottling() {
        hasShownAdsThisSession = false;
        lastAdShownTime = 0;
        Log.d(TAG, "Ad throttling reset");
    }

    /**
     * CRITICAL FIX: Update VPN notification with timer information
     */
    private void updateVpnNotificationTimer() {
        try {
            if (vpnStart && !ProConfig.isPremium(getContext())) {
                // For non-premium users, show remaining time
                if (remainingTimeInSeconds > 0) {
                    int hours = remainingTimeInSeconds / 3600;
                    int minutes = (remainingTimeInSeconds % 3600) / 60;
                    int seconds = remainingTimeInSeconds % 60;
                    String timerText = String.format("%02d:%02d remaining", hours, minutes);

                    // Import the OpenVPNService class
                    de.blinkt.openvpn.core.OpenVPNService.setTimerInfo(timerText, true);
                    Log.d(TAG, "Updated notification timer: " + timerText);
                }
            } else if (vpnStart && ProConfig.isPremium(getContext())) {
                // For premium users, show elapsed time
                if (timerStartTime > 0) {
                    long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                    int elapsedSeconds = (int) (elapsedMillis / 1000);
                    int hours = elapsedSeconds / 3600;
                    int minutes = (elapsedSeconds % 3600) / 60;
                    String timerText = String.format("%02d:%02d elapsed", hours, minutes);

                    de.blinkt.openvpn.core.OpenVPNService.setTimerInfo(timerText, true);
                    Log.d(TAG, "Updated notification timer: " + timerText);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating VPN notification timer", e);
        }
    }

    /**
     * Clear VPN notification timer information
     */
    private void clearVpnNotificationTimer() {
        try {
            de.blinkt.openvpn.core.OpenVPNService.setTimerInfo("", false);
            Log.d(TAG, "Cleared notification timer");
        } catch (Exception e) {
            Log.e(TAG, "Error clearing VPN notification timer", e);
        }
    }

    // Broadcast receiver for timer updates from background service
    BroadcastReceiver timerUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                int timeValue = intent.getIntExtra("time_value", 0);
                boolean isPremiumTimer = intent.getBooleanExtra("is_premium", false);

                if (isPremiumTimer) {
                    // Update elapsed time display for premium users
                    int hours = timeValue / 3600;
                    int minutes = (timeValue % 3600) / 60;
                    int seconds = timeValue % 60;
                    String timeStr = String.format("%02d:%02d:%02d", hours, minutes, seconds);
                    durationTv.setText(timeStr);
                } else {
                    // Update remaining time display for free users
                    remainingTimeInSeconds = timeValue;
                    updateTimerDisplay();
                }

                Log.d(TAG, "Timer updated from background service: " + timeValue);
            } catch (Exception e) {
                Log.e(TAG, "Error processing timer update", e);
            }
        }
    };

    // Broadcast receiver for timer expiration from background service
    BroadcastReceiver timerExpiredReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                Log.d(TAG, "Timer expired - disconnecting VPN");
                stopVpn();
                showToast("Time limit reached. VPN disconnected.");
                stopTimer();
            } catch (Exception e) {
                Log.e(TAG, "Error handling timer expiration", e);
            }
        }
    };

    // Broadcast receiver for timer extension from VPN timer service
    BroadcastReceiver timerExtendedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                int additionalSeconds = intent.getIntExtra("additional_seconds", 0);
                int newRemainingTime = intent.getIntExtra("new_remaining_time", 0);

                if (additionalSeconds > 0 && newRemainingTime > 0) {
                    // Update local timer variable to match service
                    remainingTimeInSeconds = newRemainingTime;

                    // Update UI display immediately
                    updateTimerDisplay();

                    int minutes = additionalSeconds / 60;
                    Log.d(TAG, "TIMER DEBUG: Received timer extension from service: " + additionalSeconds + " seconds");
                    Log.d(TAG, "TIMER DEBUG: Updated UI remainingTimeInSeconds = " + remainingTimeInSeconds);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error handling timer extension from service", e);
            }
        }
    };

    // Broadcast receiver for time extension from warning dialog
    BroadcastReceiver timeExtendedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                int additionalSeconds = intent.getIntExtra("additional_seconds", 0);

                if (additionalSeconds > 0) {
                    // CRITICAL FIX: Update local timer variable for UI display
                    // The VPN timer service has already been extended directly
                    remainingTimeInSeconds += additionalSeconds;

                    // Update UI display immediately
                    updateTimerDisplay();

                    int minutes = additionalSeconds / 60;
                    showToast("Added " + minutes + " minutes to your VPN time!");
                    Log.d(TAG, "TIMER DEBUG: UI updated with time extension of " + additionalSeconds + " seconds");
                    Log.d(TAG, "TIMER DEBUG: New UI remainingTimeInSeconds = " + remainingTimeInSeconds);

                    // Note: No need to restart timer service as it's already been extended directly
                }
            } catch (Exception e) {
                Log.e(TAG, "Error handling time extension", e);
            }
        }
    };
}
