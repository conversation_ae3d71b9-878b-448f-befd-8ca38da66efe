<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <!-- Warning Icon -->
    <ImageView
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_warning"
        android:tint="@color/warning_color" />

    <!-- Title -->
    <TextView
        android:id="@+id/warning_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="VPN Time Warning"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="12dp" />

    <!-- Message -->
    <TextView
        android:id="@+id/warning_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Only 60 seconds remaining!\nChoose an option to continue:"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="24dp" />

    <!-- Buttons Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="8dp">

        <!-- Watch Ad Button -->
        <Button
            android:id="@+id/btn_watch_ad"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Watch Ad (+5 Minutes)"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/button_primary"
            android:layout_marginBottom="12dp"
            android:elevation="2dp" />

        <!-- Upgrade Premium Button -->
        <Button
            android:id="@+id/btn_upgrade_premium"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Upgrade to Premium"
            android:textColor="@color/premium_text"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/button_premium"
            android:layout_marginBottom="12dp"
            android:elevation="2dp" />

        <!-- Dismiss Button -->
        <Button
            android:id="@+id/btn_dismiss"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:text="Continue with Current Time"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:background="@drawable/button_secondary"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</LinearLayout>
