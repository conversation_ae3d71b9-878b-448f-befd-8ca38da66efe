<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="7dp"
    android:layout_marginTop="5dp"
    android:layout_marginEnd="7dp"
    android:layout_marginBottom="5dp"
    app:cardBackgroundColor="@color/accent"
    app:cardCornerRadius="7dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp">

        <ImageView
            android:id="@+id/flag"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="5dp"
            android:background="@drawable/white_bg"
            android:padding="2dp"
            tools:src="@drawable/ic_japan" />

        <TextView
            android:id="@+id/countryName"
            style="@style/Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:layout_toStartOf="@id/btn_selected"
            android:layout_toEndOf="@+id/flag"
            android:gravity="start"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="Japan - Tokyo" />

        <ImageView
            android:id="@+id/vip_badge"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:layout_toStartOf="@id/btn_selected"
            android:src="@drawable/crown"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/btn_selected"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentEnd="true"
            android:padding="10dp"
            android:src="@drawable/ic_signal"
            app:tint="@color/colorPrimary" />


    </RelativeLayout>
</androidx.cardview.widget.CardView>
