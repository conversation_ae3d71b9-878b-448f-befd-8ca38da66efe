<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:versionCode="12" android:versionName="12" package="com.official.fivegfastvpn">
        
    <uses-sdk android:minSdkVersion="23" android:targetSdkVersion="35"/>
        
    <uses-permission android:name="android.permission.INTERNET"/>
        
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
        
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
        
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"/>
        
    <uses-permission android:name="com.android.vending.BILLING"/>
        
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
        
    <!-- VPN Service Permissions -->
        
    <uses-permission android:name="android.permission.BIND_VPN_SERVICE"/>
        
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
        
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
        
    <queries>
                
        <intent>
                        
            <action android:name="com.android.vending.billing.InAppBillingService.BIND"/>
                    
        </intent>
                
        <intent>
                        
            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND"/>
                    
        </intent>
                
        <package android:name="com.facebook.katana"/>
         
        <!-- For browser content -->
                
        <intent>
                        
            <action android:name="android.intent.action.VIEW"/>
                        
            <category android:name="android.intent.category.BROWSABLE"/>
                        
            <data android:scheme="https"/>
                    
        </intent>
         
        <!-- End of browser content -->
                
        <!-- For CustomTabsService -->
                
        <intent>
                        
            <action android:name="android.support.customtabs.action.CustomTabsService"/>
                    
        </intent>
         
        <!-- End of CustomTabsService -->
                
        <!-- For MRAID capabilities -->
                
        <intent>
                        
            <action android:name="android.intent.action.INSERT"/>
                        
            <data android:mimeType="vnd.android.cursor.dir/event"/>
                    
        </intent>
                
        <intent>
                        
            <action android:name="android.intent.action.VIEW"/>
                        
            <data android:scheme="sms"/>
                    
        </intent>
                
        <intent>
                        
            <action android:name="android.intent.action.DIAL"/>
                        
            <data android:path="tel:"/>
                    
        </intent>
            
    </queries>
        
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
     
    <!-- Required by older versions of Google Play services to create IID tokens -->
        
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE"/>
        
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
        
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID"/>
        
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION"/>
        
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS"/>
        
    <permission android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" android:protectionLevel="signature"/>
        
    <uses-permission android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"/>
        
    <application android:allowBackup="true" android:appComponentFactory="androidx.core.app.CoreComponentFactory" android:dataExtractionRules="@xml/data_extraction_rules" android:extractNativeLibs="true" android:fullBackupContent="@xml/backup_rules" android:icon="@mipmap/ic_launcher" android:label="@string/app_name" android:name="com.official.fivegfastvpn.VPNApplication" android:networkSecurityConfig="@xml/network_security_config" android:roundIcon="@mipmap/ic_launcher_round" android:supportsRtl="true" android:theme="@style/Base.Theme._5GSMARTVPNInfo" android:usesCleartextTraffic="true">
                
        <activity android:name="com.official.fivegfastvpn.activity.NotificationsActivity"/>
                
        <activity android:name="com.official.fivegfastvpn.pro.PremiumActivity"/>
                
        <activity android:name="com.official.fivegfastvpn.MainActivity"/>
                
        <activity android:name="com.official.fivegfastvpn.activity.ServersActivity"/>
                
        <activity android:excludeFromRecents="true" android:launchMode="singleTop" android:name="com.official.fivegfastvpn.activity.VpnWarningActivity" android:taskAffinity="" android:theme="@style/CustomAlertDialog"/>
                
        <activity android:exported="true" android:name="com.official.fivegfastvpn.SplashActivity">
                        
            <intent-filter>
                                
                <action android:name="android.intent.action.MAIN"/>
                                
                <category android:name="android.intent.category.LAUNCHER"/>
                            
            </intent-filter>
                    
        </activity>
                
        <activity android:excludeFromRecents="true" android:name="de.blinkt.openvpn.DisconnectVPNActivity" android:noHistory="true" android:taskAffinity=".DisconnectVPN" android:theme="@style/blinkt.dialog"/>
                
        <service android:exported="true" android:foregroundServiceType="dataSync|dataSync" android:name="de.blinkt.openvpn.core.OpenVPNService" android:permission="android.permission.BIND_VPN_SERVICE">
                        
            <intent-filter>
                                
                <action android:name="android.net.VpnService"/>
                            
            </intent-filter>
                    
        </service>
                
        <!-- VPN Timer Background Service -->
                
        <service android:exported="false" android:foregroundServiceType="dataSync" android:name="com.official.fivegfastvpn.service.VpnTimerService"/>
                
        <service android:exported="false" android:name="com.official.fivegfastvpn.MyFirebaseMessagingService">
                        
            <intent-filter>
                                
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
                            
            </intent-filter>
                    
        </service>
                
        <meta-data android:name="com.google.android.gms.ads.APPLICATION_ID" android:value="ca-app-pub-5193340328939721~2015388624"/>
                
        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value=""/>
                
        <!-- Required: set your sentry.io project identifier (DSN) -->
                
        <meta-data android:name="io.sentry.dsn" android:value="https://<EMAIL>/4508793236488192"/>
                
        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
                
        <meta-data android:name="io.sentry.traces.user-interaction.enable" android:value="true"/>
                
        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
                
        <meta-data android:name="io.sentry.attach-screenshot" android:value="true"/>
                
        <!-- enable view hierarchy for crashes -->
                
        <meta-data android:name="io.sentry.attach-view-hierarchy" android:value="true"/>
                
        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
                
        <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0"/>
                
        <!-- VpnService-based OpenVPN implementation (V2) -->
                
        <service android:exported="true" android:foregroundServiceType="dataSync" android:name="de.blinkt.openvpn.core.OpenVPNServiceV2" android:permission="android.permission.BIND_VPN_SERVICE">
                        
            <intent-filter>
                                
                <action android:name="android.net.VpnService"/>
                            
            </intent-filter>
                    
        </service>
                
        <meta-data android:name="com.google.android.play.billingclient.version" android:value="7.1.1"/>
                
        <activity android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation" android:exported="false" android:name="com.android.billingclient.api.ProxyBillingActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
                
        <activity android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation" android:exported="false" android:name="com.android.billingclient.api.ProxyBillingActivityV2" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
                
        <receiver android:exported="true" android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver" android:permission="com.google.android.c2dm.permission.SEND">
                        
            <intent-filter>
                                
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
                            
            </intent-filter>
                        
            <meta-data android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED" android:value="true"/>
                    
        </receiver>
                
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
                
        <service android:directBootAware="true" android:exported="false" android:name="com.google.firebase.messaging.FirebaseMessagingService">
                        
            <intent-filter android:priority="-500">
                                
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
                            
            </intent-filter>
                    
        </service>
                
        <service android:directBootAware="true" android:exported="false" android:name="com.google.firebase.components.ComponentDiscoveryService">
                        
            <meta-data android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
                        
            <meta-data android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
                        
            <meta-data android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
                        
            <meta-data android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
                        
            <meta-data android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
                        
            <meta-data android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
                        
            <meta-data android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
                    
        </service>
                
        <activity android:exported="false" android:name="com.google.android.gms.common.api.GoogleApiActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
                
        <activity android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout" android:exported="false" android:name="com.facebook.ads.AudienceNetworkActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
                
        <provider android:authorities="com.official.fivegfastvpn.AudienceNetworkContentProvider" android:exported="false" android:name="com.facebook.ads.AudienceNetworkContentProvider"/>
         
        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
                
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize" android:exported="false" android:name="com.google.android.gms.ads.AdActivity" android:theme="@android:style/Theme.Translucent"/>
                
        <provider android:authorities="com.official.fivegfastvpn.mobileadsinitprovider" android:exported="false" android:initOrder="100" android:name="com.google.android.gms.ads.MobileAdsInitProvider"/>
                
        <service android:enabled="true" android:exported="false" android:name="com.google.android.gms.ads.AdService"/>
                
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize" android:exported="false" android:name="com.google.android.gms.ads.OutOfContextTestingActivity"/>
                
        <activity android:excludeFromRecents="true" android:exported="false" android:launchMode="singleTask" android:name="com.google.android.gms.ads.NotificationHandlerActivity" android:taskAffinity="" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
                
        <meta-data android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING" android:value="true"/>
                
        <meta-data android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION" android:value="true"/>
                
        <provider android:authorities="com.official.fivegfastvpn.firebaseinitprovider" android:directBootAware="true" android:exported="false" android:initOrder="100" android:name="com.google.firebase.provider.FirebaseInitProvider"/>
                
        <provider android:authorities="com.official.fivegfastvpn.androidx-startup" android:exported="false" android:name="androidx.startup.InitializationProvider">
                        
            <meta-data android:name="androidx.work.WorkManagerInitializer" android:value="androidx.startup"/>
                        
            <meta-data android:name="androidx.emoji2.text.EmojiCompatInitializer" android:value="androidx.startup"/>
                        
            <meta-data android:name="androidx.lifecycle.ProcessLifecycleInitializer" android:value="androidx.startup"/>
                        
            <meta-data android:name="androidx.profileinstaller.ProfileInstallerInitializer" android:value="androidx.startup"/>
                    
        </provider>
                
        <service android:directBootAware="false" android:enabled="@bool/enable_system_alarm_service_default" android:exported="false" android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"/>
                
        <service android:directBootAware="false" android:enabled="@bool/enable_system_job_service_default" android:exported="true" android:name="androidx.work.impl.background.systemjob.SystemJobService" android:permission="android.permission.BIND_JOB_SERVICE"/>
                
        <service android:directBootAware="false" android:enabled="@bool/enable_system_foreground_service_default" android:exported="false" android:name="androidx.work.impl.foreground.SystemForegroundService"/>
                
        <receiver android:directBootAware="false" android:enabled="true" android:exported="false" android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"/>
                
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy">
                        
            <intent-filter>
                                
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED"/>
                                
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy">
                        
            <intent-filter>
                                
                <action android:name="android.intent.action.BATTERY_OKAY"/>
                                
                <action android:name="android.intent.action.BATTERY_LOW"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy">
                        
            <intent-filter>
                                
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW"/>
                                
                <action android:name="android.intent.action.DEVICE_STORAGE_OK"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy">
                        
            <intent-filter>
                                
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver">
                        
            <intent-filter>
                                
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                                
                <action android:name="android.intent.action.TIME_SET"/>
                                
                <action android:name="android.intent.action.TIMEZONE_CHANGED"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <receiver android:directBootAware="false" android:enabled="@bool/enable_system_alarm_service_default" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver">
                        
            <intent-filter>
                                
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <receiver android:directBootAware="false" android:enabled="true" android:exported="true" android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver" android:permission="android.permission.DUMP">
                        
            <intent-filter>
                                
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <service android:directBootAware="true" android:exported="false" android:name="androidx.room.MultiInstanceInvalidationService"/>
                
        <uses-library android:name="androidx.window.extensions" android:required="false"/>
                
        <uses-library android:name="androidx.window.sidecar" android:required="false"/>
         
        <!-- 'android:authorities' must be unique in the device, across all apps -->
                
        <provider android:authorities="com.official.fivegfastvpn.SentryInitProvider" android:exported="false" android:name="io.sentry.android.core.SentryInitProvider"/>
                
        <provider android:authorities="com.official.fivegfastvpn.SentryPerformanceProvider" android:exported="false" android:initOrder="200" android:name="io.sentry.android.core.SentryPerformanceProvider"/>
                
        <uses-library android:name="android.ext.adservices" android:required="false"/>
                
        <meta-data android:name="com.google.android.gms.version" android:value="@integer/google_play_services_version"/>
                
        <receiver android:directBootAware="false" android:enabled="true" android:exported="true" android:name="androidx.profileinstaller.ProfileInstallReceiver" android:permission="android.permission.DUMP">
                        
            <intent-filter>
                                
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE"/>
                            
            </intent-filter>
                        
            <intent-filter>
                                
                <action android:name="androidx.profileinstaller.action.SKIP_FILE"/>
                            
            </intent-filter>
                        
            <intent-filter>
                                
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE"/>
                            
            </intent-filter>
                        
            <intent-filter>
                                
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION"/>
                            
            </intent-filter>
                    
        </receiver>
                
        <service android:exported="false" android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery">
                        
            <meta-data android:name="backend:com.google.android.datatransport.cct.CctBackendFactory" android:value="cct"/>
                    
        </service>
                
        <service android:exported="false" android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService" android:permission="android.permission.BIND_JOB_SERVICE">
        </service>
                
        <receiver android:exported="false" android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"/>
         
        <!-- The activities will be merged into the manifest of the hosting app. -->
                
        <activity android:exported="false" android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity" android:stateNotNeeded="true" android:theme="@style/Theme.PlayCore.Transparent"/>
            
        <meta-data android:name="io.sentry.gradle-plugin-integrations" android:value="AppStartInstrumentation,DatabaseInstrumentation,FileIOInstrumentation,LogcatInstrumentation,SourceContext"/>
    </application>
    
</manifest>
