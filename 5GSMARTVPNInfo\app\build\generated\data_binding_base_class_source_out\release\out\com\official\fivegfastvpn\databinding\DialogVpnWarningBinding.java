// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogVpnWarningBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnDismiss;

  @NonNull
  public final Button btnUpgradePremium;

  @NonNull
  public final Button btnWatchAd;

  @NonNull
  public final TextView warningMessage;

  @NonNull
  public final TextView warningTitle;

  private DialogVpnWarningBinding(@NonNull LinearLayout rootView, @NonNull Button btnDismiss,
      @NonNull Button btnUpgradePremium, @NonNull Button btnWatchAd,
      @NonNull TextView warningMessage, @NonNull TextView warningTitle) {
    this.rootView = rootView;
    this.btnDismiss = btnDismiss;
    this.btnUpgradePremium = btnUpgradePremium;
    this.btnWatchAd = btnWatchAd;
    this.warningMessage = warningMessage;
    this.warningTitle = warningTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogVpnWarningBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogVpnWarningBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_vpn_warning, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogVpnWarningBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_dismiss;
      Button btnDismiss = ViewBindings.findChildViewById(rootView, id);
      if (btnDismiss == null) {
        break missingId;
      }

      id = R.id.btn_upgrade_premium;
      Button btnUpgradePremium = ViewBindings.findChildViewById(rootView, id);
      if (btnUpgradePremium == null) {
        break missingId;
      }

      id = R.id.btn_watch_ad;
      Button btnWatchAd = ViewBindings.findChildViewById(rootView, id);
      if (btnWatchAd == null) {
        break missingId;
      }

      id = R.id.warning_message;
      TextView warningMessage = ViewBindings.findChildViewById(rootView, id);
      if (warningMessage == null) {
        break missingId;
      }

      id = R.id.warning_title;
      TextView warningTitle = ViewBindings.findChildViewById(rootView, id);
      if (warningTitle == null) {
        break missingId;
      }

      return new DialogVpnWarningBinding((LinearLayout) rootView, btnDismiss, btnUpgradePremium,
          btnWatchAd, warningMessage, warningTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
