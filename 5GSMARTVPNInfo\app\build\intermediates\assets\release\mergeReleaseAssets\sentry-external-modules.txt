androidx.activity:activity-ktx:1.10.1
androidx.activity:activity:1.10.1
androidx.annotation:annotation-experimental:1.4.1
androidx.annotation:annotation-jvm:1.9.1
androidx.appcompat:appcompat-resources:1.7.0
androidx.appcompat:appcompat:1.7.0
androidx.arch.core:core-common:2.2.0
androidx.arch.core:core-runtime:2.2.0
androidx.browser:browser:1.8.0
androidx.cardview:cardview:1.0.0
androidx.collection:collection-jvm:1.4.2
androidx.collection:collection-ktx:1.4.2
androidx.concurrent:concurrent-futures-ktx:1.1.0
androidx.concurrent:concurrent-futures:1.1.0
androidx.constraintlayout:constraintlayout-core:1.1.1
androidx.constraintlayout:constraintlayout:2.2.1
androidx.coordinatorlayout:coordinatorlayout:1.1.0
androidx.core:core-ktx:1.16.0
androidx.core:core-viewtree:1.0.0
androidx.core:core:1.16.0
androidx.cursoradapter:cursoradapter:1.0.0
androidx.customview:customview:1.1.0
androidx.databinding:databinding-adapters:8.10.1
androidx.databinding:databinding-common:8.10.1
androidx.databinding:databinding-ktx:8.10.1
androidx.databinding:databinding-runtime:8.10.1
androidx.databinding:viewbinding:8.10.1
androidx.documentfile:documentfile:1.0.0
androidx.drawerlayout:drawerlayout:1.1.1
androidx.dynamicanimation:dynamicanimation:1.0.0
androidx.emoji2:emoji2-views-helper:1.3.0
androidx.emoji2:emoji2:1.3.0
androidx.exifinterface:exifinterface:1.3.6
androidx.fragment:fragment-ktx:1.6.2
androidx.fragment:fragment:1.6.2
androidx.interpolator:interpolator:1.0.0
androidx.legacy:legacy-support-core-utils:1.0.0
androidx.lifecycle:lifecycle-common-java8:2.9.0
androidx.lifecycle:lifecycle-common-jvm:2.9.0
androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0
androidx.lifecycle:lifecycle-livedata-core:2.9.0
androidx.lifecycle:lifecycle-livedata:2.9.0
androidx.lifecycle:lifecycle-process:2.9.0
androidx.lifecycle:lifecycle-runtime-android:2.9.0
androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0
androidx.lifecycle:lifecycle-service:2.9.0
androidx.lifecycle:lifecycle-viewmodel-android:2.9.0
androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0
androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0
androidx.lifecycle:lifecycle-viewmodel:2.9.0
androidx.loader:loader:1.0.0
androidx.localbroadcastmanager:localbroadcastmanager:1.1.0
androidx.navigation:navigation-common-android:2.9.0
androidx.navigation:navigation-fragment:2.9.0
androidx.navigation:navigation-runtime-android:2.9.0
androidx.navigation:navigation-ui:2.9.0
androidx.print:print:1.0.0
androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05
androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05
androidx.profileinstaller:profileinstaller:1.4.0
androidx.recyclerview:recyclerview:1.1.0
androidx.resourceinspection:resourceinspection-annotation:1.0.1
androidx.room:room-common:2.6.1
androidx.room:room-ktx:2.6.1
androidx.room:room-runtime:2.6.1
androidx.savedstate:savedstate-android:1.3.0
androidx.savedstate:savedstate-ktx:1.3.0
androidx.slidingpanelayout:slidingpanelayout:1.2.0
androidx.sqlite:sqlite-framework:2.4.0
androidx.sqlite:sqlite:2.4.0
androidx.startup:startup-runtime:1.1.1
androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
androidx.tracing:tracing-ktx:1.2.0
androidx.tracing:tracing:1.2.0
androidx.transition:transition:1.5.0
androidx.vectordrawable:vectordrawable-animated:1.1.0
androidx.vectordrawable:vectordrawable:1.1.0
androidx.versionedparcelable:versionedparcelable:1.1.1
androidx.viewpager2:viewpager2:1.0.0
androidx.viewpager:viewpager:1.0.0
androidx.webkit:webkit:1.11.0-alpha02
androidx.window:window:1.0.0
androidx.work:work-runtime:2.10.1
com.airbnb.android:lottie:6.6.1
com.android.billingclient:billing:7.1.1
com.android.volley:volley:1.2.1
com.facebook.android:audience-network-sdk:6.20.0
com.facebook.infer.annotation:infer-annotation:0.18.0
com.getkeepsafe.relinker:relinker:1.4.5
com.github.bumptech.glide:annotations:4.16.0
com.github.bumptech.glide:disklrucache:4.16.0
com.github.bumptech.glide:gifdecoder:4.16.0
com.github.bumptech.glide:glide:4.16.0
com.google.ads.mediation:facebook:********
com.google.android.datatransport:transport-api:3.1.0
com.google.android.datatransport:transport-backend-cct:3.1.9
com.google.android.datatransport:transport-runtime:3.1.9
com.google.android.gms:play-services-ads-api:24.3.0
com.google.android.gms:play-services-ads-identifier:18.0.0
com.google.android.gms:play-services-ads:24.3.0
com.google.android.gms:play-services-appset:16.0.1
com.google.android.gms:play-services-base:18.5.0
com.google.android.gms:play-services-basement:18.4.0
com.google.android.gms:play-services-cloud-messaging:17.2.0
com.google.android.gms:play-services-location:19.0.0
com.google.android.gms:play-services-measurement-base:20.1.2
com.google.android.gms:play-services-measurement-sdk-api:20.1.2
com.google.android.gms:play-services-places-placereport:17.0.0
com.google.android.gms:play-services-stats:17.0.2
com.google.android.gms:play-services-tasks:18.2.0
com.google.android.material:material:1.12.0
com.google.android.play:app-update:2.1.0
com.google.android.play:core-common:2.0.3
com.google.android.ump:user-messaging-platform:3.2.0
com.google.code.findbugs:jsr305:3.0.2
com.google.errorprone:error_prone_annotations:2.26.0
com.google.firebase:firebase-annotations:16.2.0
com.google.firebase:firebase-common-ktx:21.0.0
com.google.firebase:firebase-common:21.0.0
com.google.firebase:firebase-components:18.0.0
com.google.firebase:firebase-datatransport:18.2.0
com.google.firebase:firebase-encoders-json:18.0.0
com.google.firebase:firebase-encoders-proto:16.0.0
com.google.firebase:firebase-encoders:17.0.0
com.google.firebase:firebase-iid-interop:17.1.0
com.google.firebase:firebase-installations-interop:17.1.1
com.google.firebase:firebase-installations:18.0.0
com.google.firebase:firebase-measurement-connector:19.0.0
com.google.firebase:firebase-messaging:24.1.1
com.google.guava:failureaccess:1.0.1
com.google.guava:guava:31.1-android
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
com.google.j2objc:j2objc-annotations:1.3
com.squareup.okio:okio:1.17.6
io.sentry:sentry-android-core:8.1.0
io.sentry:sentry-android-fragment:8.1.0
io.sentry:sentry-android-ndk:8.1.0
io.sentry:sentry-android-replay:8.1.0
io.sentry:sentry-android-sqlite:8.1.0
io.sentry:sentry-android:8.1.0
io.sentry:sentry-kotlin-extensions:8.1.0
io.sentry:sentry-native-ndk:0.7.19
io.sentry:sentry:8.1.0
javax.inject:javax.inject:1
org.checkerframework:checker-qual:3.12.0
org.jacoco:org.jacoco.core:0.8.8
org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0
org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0
org.jetbrains.kotlin:kotlin-stdlib:2.1.10
org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3
org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3
org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3
org.jetbrains:annotations:23.0.0
org.jspecify:jspecify:1.0.0
org.ow2.asm:asm-analysis:9.2
org.ow2.asm:asm-commons:9.2
org.ow2.asm:asm-tree:9.2
org.ow2.asm:asm:9.2
pl.droidsonroids.gif:android-gif-drawable:1.2.28