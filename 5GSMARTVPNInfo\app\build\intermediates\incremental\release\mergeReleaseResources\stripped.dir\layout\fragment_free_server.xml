<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/FF5722"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/searchandrefresh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- Material Design SearchView -->
        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchView1"
            android:layout_margin="5dp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:theme="@style/CustomSearchViewStyle"
            android:layout_height="50dp"
            android:background="@drawable/searchviewback"
            android:padding="5dp"
            app:iconifiedByDefault="false"
            app:queryHint="@string/Search" />

        <!-- Refresh Button -->
        <ImageButton
            android:id="@+id/refreshButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:layout_gravity="center"
            android:background="@null"
            android:src="@drawable/autoref"
            />

    </LinearLayout>
    <ProgressBar

        android:id="@+id/progressBar"

        android:layout_width="wrap_content"

        android:layout_height="wrap_content"
        android:layout_below="@id/searchandrefresh"

        android:layout_centerInParent="true"

        android:visibility="gone" />




    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_free"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/progressBar"
        android:visibility="visible"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/item_server" />

</RelativeLayout>